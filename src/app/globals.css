@import "tailwindcss";

/* Custom CSS variables for enhanced theme support */
:root {
  /* Font family variables */
  --font-sans: var(--font-geist-sans, 'Arial', 'Helvetica', sans-serif);
  --font-mono: var(--font-geist-mono, 'Courier New', monospace);
}

/* Base body styles */
body {
  font-family: var(--font-sans);
  /* Colors are applied via Tailwind classes in layout.tsx */
}

/* Ensure smooth transitions for color changes */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}