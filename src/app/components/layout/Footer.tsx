// src/app/components/layout/Footer.tsx
import Link from 'next/link';
import { FaInstagram, FaFacebookF, FaWhatsapp } from 'react-icons/fa';
import Image from 'next/image'; // Para o logo no footer

export default function Footer() {
  const currentYear = new Date().getFullYear();
  return (
    <footer className="bg-footer-bg text-light-text pt-12 pb-8">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-8 mb-10 text-center md:text-left">

          <div>
            <h5 className="text-lg font-semibold mb-4 text-light-text">Navegue</h5>
            <ul className="space-y-2">
              <li><Link href="/" className="text-light-text hover:text-primary transition-colors text-sm">Home</Link></li>
              <li><Link href="/cardapio" className="text-light-text hover:text-primary transition-colors text-sm">Cardápio</Link></li>
              <li><Link href="/sobre" className="text-light-text hover:text-primary transition-colors text-sm">Sobre Nós</Link></li>
              <li><Link href="/galeria" className="text-light-text hover:text-primary transition-colors text-sm">Galeria</Link></li>
              <li><Link href="/contato" className="text-light-text hover:text-primary transition-colors text-sm">Contato</Link></li>
              <li><Link href="/reservas" className="text-light-text hover:text-primary transition-colors text-sm">Reservas</Link></li>
            </ul>
          </div>

          <div>
            <h5 className="text-lg font-semibold mb-4 text-light-text">Contato</h5>
            <address className="not-italic text-sm space-y-2 text-light-text">
              <p>Rua das Delícias, 123</p>
              <p>Cidade Gastronômica, ES</p>
              <p>
                <a href="tel:+5527999998888" className="text-light-text hover:text-primary transition-colors">(27) 99999-8888</a>
              </p>
              <p>
                <a href="mailto:<EMAIL>" className="text-light-text hover:text-primary transition-colors">
                  <EMAIL>
                </a>
              </p>
            </address>
          </div>
          
          <div>
            <h5 className="text-lg font-semibold mb-4 text-light-text">Siga-nos</h5>
            <div className="flex justify-center md:justify-start space-x-5">
              <a href="https://instagram.com" target="_blank" rel="noopener noreferrer" aria-label="Instagram" className="text-light-text hover:text-primary transition-colors">
                <FaInstagram size={24} />
              </a>
              <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" aria-label="Facebook" className="text-light-text hover:text-primary transition-colors">
                <FaFacebookF size={24} />
              </a>
              <a href="https://wa.me/5527999998888" target="_blank" rel="noopener noreferrer" aria-label="WhatsApp" className="text-light-text hover:text-primary transition-colors">
                <FaWhatsapp size={24} />
              </a>
            </div>
          </div>
        </div>
        <div className="border-t border-eerie_black-600 pt-8 text-center text-xs text-text-muted-on-dark">
          <p>© {currentYear} Restaurante Sabor Caseiro. Todos os direitos reservados.</p>
          <p className="mt-1">
            Design por Matheus Achim.
          </p>
        </div>
      </div>
    </footer>
  );
}